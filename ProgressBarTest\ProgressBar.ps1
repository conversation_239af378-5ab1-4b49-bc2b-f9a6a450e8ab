param(
    [int]$columns = $host.ui.rawui.windowsize.width,
    [int]$lines = $host.ui.rawui.windowsize.height
)

Write-Host "`e[1;${($lines - 2)}r"

function Show-ProgressBar {
    param(
        [int]$PercentComplete,
        [string]$Message = "",
        [int]$barWidth = $columns - ($columns / 5),
        [int]$filledWidth = (($PercentComplete * $barWidth) / 100),
        [int]$emptyWidth = $barWidth - $filledWidth,
        [string]$filledChar = "•" * $filledWidth,
        [string]$emptyChar = " " * $emptyWidth
    )
    Write-Host "`e7"
    Write-Host "`e[r"
    Write-Host "`e[${($lines - 1)};1H`r[$filledChar$emptyChar] $PercentComplete% $Message"
    Write-Host "`e8"
    Write-Host "`e[1;${($lines - 2)}r"
}

function Main {
    Clear-Host
    Write-Host "`e[?25l"

    try {
        for ($i = 0; $i -le 100; $i++) {
            Show-ProgressBar -PercentComplete $i -Message "Processing..."
            #Write-Host "Processing Number $i"
            Start-Sleep -Milliseconds 100
        }
    } finally {
        Write-Host "`e[?25h"
    }

    Write-Host "`e[r"
}

Main

